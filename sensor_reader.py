#!/usr/bin/env python
# -*- coding: utf-8 -*-

import argparse
import asyncio
from datetime import datetime
from pymodbus.client import AsyncModbusSerialClient, AsyncModbusTcpClient
from extension_client import AsyncModbusExtensionClient

# 默认配置参数
DEFAULT_SENSOR_COUNT = 1      # 默认传感器数量
SENSOR_DATA_START_ADDR = 10000  # 温湿度水分传感器起始地址
SENSOR_DATA_LENGTH = 8        # 每个传感器数据长度(字节)
SLAVE_ADDRESS = 1             # 默认Modbus从站地址

def create_trace_functions(verbose=False):
    """创建用于跟踪通讯数据的函数"""
    if not verbose:
        return None, None

    def trace_packet(sending, data):
        """跟踪数据包字节内容"""
        direction = "发送" if sending else "接收"
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        hex_data = ' '.join(f'{b:02X}' for b in data)
        print(f"[{timestamp}] [{direction}] 字节数据: {hex_data}")
        print(f"[{timestamp}] [{direction}] 数据长度: {len(data)} 字节")
        return data

    def trace_pdu(sending, pdu):
        """跟踪PDU内容"""
        direction = "发送" if sending else "接收"
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        slave_id = getattr(pdu, 'slave_id', getattr(pdu, 'unit_id', getattr(pdu, 'dev_id', 'N/A')))
        print(f"[{timestamp}] [{direction}] PDU: 功能码={pdu.function_code}, 从站={slave_id}")
        return pdu

    return trace_packet, trace_pdu

def parse_sensor_data(registers):
    """解析传感器数据"""
    # 每个传感器数据占4个寄存器(8字节)
    # 数据结构: [缆号, 节点号, 温度, 湿度, 水分]
    cable = registers[0] >> 8
    node = registers[0] & 0xFF
    temperature = registers[1] / 10.0      # 温度除以10
    humidity = registers[2] / 10.0        # 湿度除以100
    moisture = registers[3] / 100.0        # 水分除以100
    
    return {
        'cable': cable,
        'node': node,
        'temperature': temperature,
        'humidity': humidity,
        'moisture': moisture
    }

async def read_sensors(client, sensor_count):
    """读取指定数量的传感器数据"""
    results = []

    # 计算需要读取的寄存器数量(每个传感器4个寄存器)
    register_count = sensor_count * 4

    # 读取传感器数据
    response = await client.read_holding_registers(
        address=SENSOR_DATA_START_ADDR,
        count=register_count,
        slave=SLAVE_ADDRESS
    )

    if response.isError():
        raise Exception(f"Modbus错误: {response}")

    # 解析每个传感器的数据
    for i in range(sensor_count):
        start = i * 4
        sensor_registers = response.registers[start:start+4]
        sensor_data = parse_sensor_data(sensor_registers)
        results.append(
            f"{sensor_data['temperature']:.1f}/"
            f"{sensor_data['humidity']:.1f}/"
            f"{sensor_data['moisture']:.2f}"
        )

    return results

async def async_main():
    """异步主函数"""
    parser = argparse.ArgumentParser(
        description='温湿度水分三合一传感器数据读取器',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument('--count', type=int, default=DEFAULT_SENSOR_COUNT,
                      help='传感器数量')
    parser.add_argument('--address', type=int, default=SLAVE_ADDRESS,
                      help='Modbus从站地址')
    parser.add_argument('--serial', help='串口设备路径(如/dev/ttyUSB0)')
    parser.add_argument('--baudrate', type=int, default=9600,
                      help='波特率')
    parser.add_argument('--ip', help='TCP/IP地址(如*************)')
    parser.add_argument('--port', type=int, default=502,
                      help='TCP端口')
    parser.add_argument('--extension', help='分机IP地址(如*************)')
    parser.add_argument('--extension-port', type=int, default=3456,
                      help='分机端口')
    parser.add_argument('--extension-serial', default='dev/ttyS2',
                      help='分机串口设备路径')
    parser.add_argument('--extension-baudrate', type=int, default=9600,
                      help='分机串口波特率')
    parser.add_argument('--extension-timeout', type=int, default=1000,
                      help='分机超时时间(毫秒)')
    parser.add_argument('--verbose', '-v', action='store_true',
                      help='显示详细的通讯协议字节内容')
    args = parser.parse_args()

    # 创建 trace 函数
    trace_packet, trace_pdu = create_trace_functions(args.verbose)

    if args.verbose:
        print("=== 启用详细通讯日志 ===")

    # 创建异步Modbus客户端
    if args.serial:
        client = AsyncModbusSerialClient(
            port=args.serial,
            baudrate=args.baudrate,
            bytesize=8,
            parity='N',
            stopbits=1,
            timeout=5,  # 增加超时时间
            retries=5,  # 增加重试次数
            framer='rtu',
            trace_packet=trace_packet,
            trace_pdu=trace_pdu
        )
    elif args.ip:
        client = AsyncModbusTcpClient(
            host=args.ip,
            port=args.port,
            timeout=10,  # 增加超时时间到10秒
            retries=5,   # 增加重试次数
            reconnect_delay=0.1,  # 快速重连
            reconnect_delay_max=2.0,  # 最大重连延迟
            framer='rtu',  # 保持RTU framer用于Modbus RTU over TCP
            trace_packet=trace_packet,
            trace_pdu=trace_pdu
        )
    elif args.extension:
        client = AsyncModbusExtensionClient(
            host=args.extension,
            port=args.extension_port,
            serial_port=args.extension_serial,
            baudrate=args.extension_baudrate,
            timeout=args.extension_timeout,
            framer='rtu',
            retries=5,
            trace_packet=trace_packet,
            trace_pdu=trace_pdu
        )
    else:
        print("错误: 必须指定--serial、--ip或--extension参数")
        return

    # 连接设备并读取数据
    try:
        if args.verbose:
            connection_target = args.extension or args.ip or args.serial
            print(f"正在连接到 {connection_target}...")

        # 尝试连接，增加连接检查
        connected = await client.connect()
        if not connected:
            print("错误: 无法连接到设备")
            return

        if args.verbose:
            print("连接成功，正在读取数据...")

        # 获取当前时间
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 读取传感器数据
        sensor_data = await read_sensors(client, args.count)

        # 输出结果
        print(f"{current_time} {' '.join(sensor_data)}")

    except asyncio.TimeoutError:
        print("错误: 连接或读取超时，请检查网络连接和设备状态")
    except ConnectionError as e:
        print(f"错误: 连接失败 - {e}")
    except Exception as e:
        print(f"错误: {e}")
    finally:
        client.close()

def main():
    """同步包装函数"""
    asyncio.run(async_main())

if __name__ == "__main__":
    main()
